-- 测试环境数据库初始化脚本
-- 创建测试数据和基础配置

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建测试用户数据
INSERT INTO users (id, username, email, password_hash, role, status, created_at, updated_at) 
VALUES 
  (uuid_generate_v4(), 'testadmin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', NOW(), NOW()),
  (uuid_generate_v4(), 'testuser', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'active', NOW(), NOW()),
  (uuid_generate_v4(), 'testmanager', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'manager', 'active', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 创建测试商品分类
INSERT INTO categories (id, name, description, parent_id, sort_order, status, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), '电子产品', '各类电子设备和配件', NULL, 1, 'active', NOW(), NOW()),
  (uuid_generate_v4(), '服装鞋帽', '服装、鞋子、帽子等', NULL, 2, 'active', NOW(), NOW()),
  (uuid_generate_v4(), '家居用品', '家庭日用品和装饰品', NULL, 3, 'active', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- 创建测试商品数据
INSERT INTO products (id, sku, name, description, category_id, price, cost_price, stock_quantity, min_stock, status, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), 'TEST-001', '测试商品1', '这是一个测试商品', (SELECT id FROM categories WHERE name = '电子产品' LIMIT 1), 99.99, 50.00, 100, 10, 'active', NOW(), NOW()),
  (uuid_generate_v4(), 'TEST-002', '测试商品2', '这是另一个测试商品', (SELECT id FROM categories WHERE name = '服装鞋帽' LIMIT 1), 199.99, 100.00, 50, 5, 'active', NOW(), NOW()),
  (uuid_generate_v4(), 'TEST-003', '测试商品3', '第三个测试商品', (SELECT id FROM categories WHERE name = '家居用品' LIMIT 1), 299.99, 150.00, 25, 3, 'active', NOW(), NOW())
ON CONFLICT (sku) DO NOTHING;

-- 创建测试供应商
INSERT INTO suppliers (id, name, contact_person, email, phone, address, status, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), '测试供应商A', '张三', '<EMAIL>', '13800138001', '北京市朝阳区测试街道1号', 'active', NOW(), NOW()),
  (uuid_generate_v4(), '测试供应商B', '李四', '<EMAIL>', '13800138002', '上海市浦东新区测试路2号', 'active', NOW(), NOW()),
  (uuid_generate_v4(), '测试供应商C', '王五', '<EMAIL>', '13800138003', '广州市天河区测试大道3号', 'active', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 创建测试客户
INSERT INTO customers (id, name, email, phone, address, customer_type, status, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), '测试客户A', '<EMAIL>', '13900139001', '深圳市南山区测试小区1栋', 'individual', 'active', NOW(), NOW()),
  (uuid_generate_v4(), '测试客户B', '<EMAIL>', '13900139002', '杭州市西湖区测试花园2栋', 'enterprise', 'active', NOW(), NOW()),
  (uuid_generate_v4(), '测试客户C', '<EMAIL>', '13900139003', '成都市锦江区测试广场3栋', 'individual', 'active', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- 创建测试订单
INSERT INTO orders (id, order_no, customer_id, total_amount, status, order_date, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), 'ORD-TEST-001', (SELECT id FROM customers WHERE email = '<EMAIL>' LIMIT 1), 299.98, 'pending', NOW(), NOW(), NOW()),
  (uuid_generate_v4(), 'ORD-TEST-002', (SELECT id FROM customers WHERE email = '<EMAIL>' LIMIT 1), 199.99, 'confirmed', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', NOW()),
  (uuid_generate_v4(), 'ORD-TEST-003', (SELECT id FROM customers WHERE email = '<EMAIL>' LIMIT 1), 99.99, 'shipped', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', NOW())
ON CONFLICT (order_no) DO NOTHING;

-- 创建系统配置
INSERT INTO system_configs (key, value, description, created_at, updated_at)
VALUES 
  ('system_name', 'ERP测试系统', '系统名称', NOW(), NOW()),
  ('default_currency', 'CNY', '默认货币', NOW(), NOW()),
  ('default_language', 'zh-CN', '默认语言', NOW(), NOW()),
  ('max_upload_size', '10485760', '最大上传文件大小(字节)', NOW(), NOW()),
  ('session_timeout', '3600', '会话超时时间(秒)', NOW(), NOW())
ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  updated_at = NOW();

-- 创建权限和角色数据
INSERT INTO permissions (id, name, description, resource, action, created_at)
VALUES 
  (uuid_generate_v4(), 'user.read', '查看用户', 'user', 'read', NOW()),
  (uuid_generate_v4(), 'user.write', '编辑用户', 'user', 'write', NOW()),
  (uuid_generate_v4(), 'product.read', '查看商品', 'product', 'read', NOW()),
  (uuid_generate_v4(), 'product.write', '编辑商品', 'product', 'write', NOW()),
  (uuid_generate_v4(), 'order.read', '查看订单', 'order', 'read', NOW()),
  (uuid_generate_v4(), 'order.write', '编辑订单', 'order', 'write', NOW())
ON CONFLICT (name) DO NOTHING;

-- 输出初始化完成信息
DO $$
BEGIN
  RAISE NOTICE '=================================';
  RAISE NOTICE '测试数据库初始化完成！';
  RAISE NOTICE '=================================';
  RAISE NOTICE '测试账号信息：';
  RAISE NOTICE '管理员: <EMAIL> / password';
  RAISE NOTICE '普通用户: <EMAIL> / password';
  RAISE NOTICE '经理: <EMAIL> / password';
  RAISE NOTICE '=================================';
  RAISE NOTICE '数据统计：';
  RAISE NOTICE '用户数量: %', (SELECT COUNT(*) FROM users);
  RAISE NOTICE '商品数量: %', (SELECT COUNT(*) FROM products);
  RAISE NOTICE '订单数量: %', (SELECT COUNT(*) FROM orders);
  RAISE NOTICE '=================================';
END $$;
