#!/bin/bash
# 测试环境快速启动脚本

set -e

echo "🚀 启动ERP后端测试环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker和Docker Compose
check_dependencies() {
    echo -e "${BLUE}📋 检查依赖...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查通过${NC}"
}

# 创建必要目录
create_directories() {
    echo -e "${BLUE}📁 创建必要目录...${NC}"
    
    mkdir -p logs/{backend,nginx,postgres,redis}
    mkdir -p uploads/{images,documents,temp}
    mkdir -p backups/{database,redis}
    mkdir -p scripts
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 设置环境变量
setup_env() {
    echo -e "${BLUE}🔐 设置环境变量...${NC}"
    
    if [ ! -f ".env.test" ]; then
        cat > .env.test << EOF
# 测试环境配置
DB_PASSWORD=erp_test_password_2024
JWT_SECRET=test_jwt_secret_key_for_backend_api_testing_environment_2024
REDIS_PASSWORD=
CI_REGISTRY_IMAGE=registry.gitlab.com/your-group/your-project

# 数据库配置
POSTGRES_DB=erp_test
POSTGRES_USER=erp_user
POSTGRES_PASSWORD=erp_test_password_2024

# 应用配置
APP_ENV=test
GIN_MODE=debug
LOG_LEVEL=debug

# 外部服务配置
WEBHOOK_URL=
EOF
        echo -e "${GREEN}✅ 环境变量文件创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 环境变量文件已存在，跳过创建${NC}"
    fi
}

# 停止旧服务
stop_old_services() {
    echo -e "${BLUE}⏹️ 停止旧服务...${NC}"
    
    docker-compose -f docker-compose.backend-test.yml down --remove-orphans 2>/dev/null || true
    
    echo -e "${GREEN}✅ 旧服务已停止${NC}"
}

# 拉取镜像
pull_images() {
    echo -e "${BLUE}📦 拉取Docker镜像...${NC}"
    
    docker-compose -f docker-compose.backend-test.yml pull postgres redis pgadmin redis-insight swagger-ui filebeat
    
    echo -e "${GREEN}✅ 镜像拉取完成${NC}"
}

# 启动服务
start_services() {
    echo -e "${BLUE}▶️ 启动测试环境服务...${NC}"
    
    # 加载环境变量
    export $(cat .env.test | grep -v '^#' | xargs)
    
    # 启动服务
    docker-compose -f docker-compose.backend-test.yml up -d
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${BLUE}⏳ 等待服务就绪...${NC}"
    
    # 等待PostgreSQL
    echo -e "${YELLOW}等待PostgreSQL启动...${NC}"
    for i in {1..30}; do
        if docker-compose -f docker-compose.backend-test.yml exec -T postgres pg_isready -U erp_user -d erp_test > /dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL已就绪${NC}"
            break
        fi
        sleep 2
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ PostgreSQL启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待Redis
    echo -e "${YELLOW}等待Redis启动...${NC}"
    for i in {1..15}; do
        if docker-compose -f docker-compose.backend-test.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Redis已就绪${NC}"
            break
        fi
        sleep 2
        if [ $i -eq 15 ]; then
            echo -e "${RED}❌ Redis启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待后端服务 (如果镜像存在)
    if docker images | grep -q "backend"; then
        echo -e "${YELLOW}等待后端服务启动...${NC}"
        for i in {1..30}; do
            if curl -f http://localhost:8080/health > /dev/null 2>&1; then
                echo -e "${GREEN}✅ 后端服务已就绪${NC}"
                break
            fi
            sleep 3
            if [ $i -eq 30 ]; then
                echo -e "${YELLOW}⚠️ 后端服务未响应，可能需要CI/CD部署${NC}"
                break
            fi
        done
    else
        echo -e "${YELLOW}⚠️ 后端镜像不存在，需要通过CI/CD构建部署${NC}"
    fi
}

# 显示服务信息
show_services_info() {
    echo -e "${BLUE}📊 服务信息:${NC}"
    echo "=================================="
    echo -e "${GREEN}🔗 服务访问地址:${NC}"
    echo "  后端API:        http://localhost:8080"
    echo "  API文档:        http://localhost:8080/swagger/index.html"
    echo "  健康检查:       http://localhost:8080/health"
    echo ""
    echo -e "${GREEN}🛠️ 管理工具:${NC}"
    echo "  pgAdmin:        http://localhost:5050"
    echo "    用户名: <EMAIL>"
    echo "    密码: admin123"
    echo ""
    echo "  RedisInsight:   http://localhost:8001"
    echo ""
    echo "  Swagger UI:     http://localhost:8002"
    echo ""
    echo -e "${GREEN}🔌 数据库连接:${NC}"
    echo "  PostgreSQL:     localhost:5432"
    echo "    数据库: erp_test"
    echo "    用户名: erp_user"
    echo "    密码: erp_test_password_2024"
    echo ""
    echo "  Redis:          localhost:6379"
    echo ""
    echo -e "${GREEN}👤 测试账号:${NC}"
    echo "  管理员: <EMAIL> / password"
    echo "  用户: <EMAIL> / password"
    echo "  经理: <EMAIL> / password"
    echo "=================================="
}

# 显示常用命令
show_commands() {
    echo -e "${BLUE}📝 常用命令:${NC}"
    echo "=================================="
    echo "查看服务状态:"
    echo "  docker-compose -f docker-compose.backend-test.yml ps"
    echo ""
    echo "查看服务日志:"
    echo "  docker-compose -f docker-compose.backend-test.yml logs -f backend"
    echo "  docker-compose -f docker-compose.backend-test.yml logs -f postgres"
    echo ""
    echo "重启服务:"
    echo "  docker-compose -f docker-compose.backend-test.yml restart backend"
    echo ""
    echo "停止所有服务:"
    echo "  docker-compose -f docker-compose.backend-test.yml down"
    echo ""
    echo "清理数据重新开始:"
    echo "  docker-compose -f docker-compose.backend-test.yml down -v"
    echo "=================================="
}

# 主函数
main() {
    echo -e "${GREEN}🎯 ERP后端测试环境启动脚本${NC}"
    echo "=================================="
    
    check_dependencies
    create_directories
    setup_env
    stop_old_services
    pull_images
    start_services
    wait_for_services
    
    echo ""
    echo -e "${GREEN}🎉 测试环境启动完成！${NC}"
    echo ""
    
    show_services_info
    echo ""
    show_commands
    
    echo ""
    echo -e "${YELLOW}💡 提示: 后端服务需要通过GitLab CI/CD构建部署${NC}"
    echo -e "${YELLOW}   推送代码到test分支即可自动部署后端服务${NC}"
}

# 执行主函数
main "$@"
