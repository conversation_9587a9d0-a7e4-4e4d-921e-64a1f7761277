# GitLab CI/CD 配置文件
stages:
  - build
  - test
  - deploy
  - notify

variables:
  # Docker镜像配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  IMAGE_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHORT_SHA
  LATEST_TAG: $CI_REGISTRY_IMAGE/backend:latest
  
  # 部署配置
  DEPLOY_SERVER: $ECS_SERVER_IP
  DEPLOY_USER: $ECS_USER
  PROJECT_PATH: "/opt/erp-backend"

# 构建阶段
build:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd backend
    - |
      # 构建Docker镜像
      docker build \
        --build-arg VERSION=$CI_COMMIT_SHORT_SHA \
        --build-arg BUILD_TIME=$(date '+%Y-%m-%d_%H:%M:%S') \
        --build-arg COMMIT_HASH=$CI_COMMIT_SHA \
        -f docker/Dockerfile \
        -t $IMAGE_TAG \
        -t $LATEST_TAG \
        .
    - docker push $IMAGE_TAG
    - docker push $LATEST_TAG
  only:
    - main
    - develop
    - test

# 测试阶段
test:
  stage: test
  image: golang:1.24.4-alpine
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: erp_test
    POSTGRES_USER: erp_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST_AUTH_METHOD: trust
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USER: erp_user
    DB_PASSWORD: test_password
    DB_NAME: erp_test
    REDIS_HOST: redis
    REDIS_PORT: 6379
  before_script:
    - cd backend
    - apk add --no-cache git make curl
    - go mod download
  script:
    - echo "🧪 运行单元测试..."
    - go test -v -race -coverprofile=coverage.out ./...
    - echo "📊 生成测试覆盖率报告..."
    - go tool cover -html=coverage.out -o coverage.html
    - echo "🔍 运行代码质量检查..."
    - go vet ./...
    - go fmt ./...
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage.xml
    paths:
      - backend/coverage.html
    expire_in: 1 week
  coverage: '/coverage: \d+\.\d+% of statements/'
  only:
    - main
    - develop
    - test

# 部署到测试环境
deploy_test:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $DEPLOY_SERVER >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "🚀 开始部署到测试环境..."
    - |
      ssh $DEPLOY_USER@$DEPLOY_SERVER << 'EOF'
        set -e
        
        # 进入项目目录
        cd $PROJECT_PATH || { echo "项目目录不存在"; exit 1; }
        
        # 拉取最新代码
        echo "📥 拉取最新代码..."
        git pull origin $CI_COMMIT_REF_NAME
        
        # 登录到GitLab Container Registry
        echo "🔐 登录容器镜像仓库..."
        echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
        
        # 拉取最新镜像
        echo "📦 拉取最新镜像..."
        docker pull $IMAGE_TAG
        
        # 更新docker-compose配置中的镜像标签
        sed -i "s|image:.*backend.*|image: $IMAGE_TAG|g" docker-compose.backend-test.yml
        
        # 停止旧服务
        echo "⏹️ 停止旧服务..."
        docker-compose -f docker-compose.backend-test.yml down || true
        
        # 启动新服务
        echo "▶️ 启动新服务..."
        docker-compose -f docker-compose.backend-test.yml up -d
        
        # 等待服务启动
        echo "⏳ 等待服务启动..."
        sleep 30
        
        # 健康检查
        echo "🏥 执行健康检查..."
        for i in {1..10}; do
          if curl -f http://localhost:8080/health > /dev/null 2>&1; then
            echo "✅ 服务启动成功！"
            break
          else
            echo "⏳ 等待服务启动... ($i/10)"
            sleep 10
          fi
          
          if [ $i -eq 10 ]; then
            echo "❌ 服务启动失败"
            docker-compose -f docker-compose.backend-test.yml logs backend
            exit 1
          fi
        done
        
        # 清理旧镜像
        echo "🧹 清理旧镜像..."
        docker image prune -f
        
        echo "🎉 部署完成！"
      EOF
  environment:
    name: test
    url: http://$ECS_SERVER_IP:8080
  only:
    - main
    - develop
    - test
  when: manual  # 手动触发部署

# 自动部署到测试环境 (develop分支)
deploy_test_auto:
  extends: deploy_test
  only:
    - develop
  when: on_success  # 测试通过后自动部署

# 部署通知
notify_success:
  stage: notify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      # 发送成功通知 (可以集成钉钉、企业微信等)
      curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
          \"msgtype\": \"text\",
          \"text\": {
            \"content\": \"✅ ERP后端测试环境部署成功！\n分支: $CI_COMMIT_REF_NAME\n提交: $CI_COMMIT_SHORT_SHA\n访问地址: http://$ECS_SERVER_IP:8080\"
          }
        }" || echo "通知发送失败"
  only:
    - main
    - develop
  when: on_success

notify_failure:
  stage: notify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      # 发送失败通知
      curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
          \"msgtype\": \"text\",
          \"text\": {
            \"content\": \"❌ ERP后端测试环境部署失败！\n分支: $CI_COMMIT_REF_NAME\n提交: $CI_COMMIT_SHORT_SHA\n请检查CI/CD日志\"
          }
        }" || echo "通知发送失败"
  only:
    - main
    - develop
  when: on_failure