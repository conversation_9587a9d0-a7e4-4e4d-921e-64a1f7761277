# 九翼跨境电商ERP系统 - 简化版GitLab CI/CD配置
# 专门解决Docker TLS证书问题

stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2

# 简化构建阶段（无TLS）
build:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_VERIFY: 0
  before_script:
    - echo "🔍 检查Docker环境（非TLS模式）..."
    - sleep 5
    - docker --version
    - docker info
  script:
    - echo "🏗️ 构建Docker镜像..."
    - cd backend
    - |
      # 简单构建，不推送到注册表
      docker build \
        --build-arg VERSION=${CI_COMMIT_SHORT_SHA:-dev} \
        --build-arg BUILD_TIME=$(date '+%Y-%m-%d_%H:%M:%S') \
        --build-arg COMMIT_HASH=${CI_COMMIT_SHORT_SHA:-unknown} \
        -f docker/Dockerfile \
        -t erp-backend:${CI_COMMIT_SHORT_SHA:-latest} \
        -t erp-backend:latest \
        .
      
      echo "✅ 镜像构建完成"
      docker images | grep erp-backend
  only:
    - main
    - test

# 测试阶段
test:
  stage: test
  image: golang:1.24.4-alpine
  services:
    - postgres:17-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: erp_test
    POSTGRES_USER: erp_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST_AUTH_METHOD: trust
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USER: erp_user
    DB_PASSWORD: test_password
    DB_NAME: erp_test
    REDIS_HOST: redis
    REDIS_PORT: 6379
  before_script:
    - apk add --no-cache git make curl
  script:
    - echo "🧪 运行单元测试..."
    - cd backend
    - go mod download
    - go test -v -race -coverprofile=../backend/coverage.out ./...
    - echo "📊 生成测试覆盖率报告..."
    - go tool cover -html=../backend/coverage.out -o ../backend/coverage.html
    - echo "🔄 转换覆盖率格式为Cobertura..."
    - go install github.com/boumenot/gocover-cobertura@latest
    - gocover-cobertura < ../backend/coverage.out > ../backend/coverage.xml
    - echo "🔍 运行代码质量检查..."
    - go vet ./...
    - go fmt ./...
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage.xml
    paths:
      - backend/coverage.html
      - backend/coverage.xml
      - backend/coverage.out
    expire_in: 1 week
  coverage: '/coverage: \d+\.\d+% of statements/'
  only:
    - main
    - test

# 部署到测试环境（简化版）
deploy_test:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client curl git
  script:
    - echo "🚀 部署到测试环境..."
    - echo "💡 使用本地Docker Compose构建和部署"
    - |
      if [ -n "$SSH_PRIVATE_KEY" ] && [ -n "$TEST_SERVER_HOST" ]; then
        eval $(ssh-agent -s)
        echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        ssh-keyscan -H $TEST_SERVER_HOST >> ~/.ssh/known_hosts
        
        ssh $SSH_USER@$TEST_SERVER_HOST << 'EOF'
          cd /opt/9-wings-erp
          echo "📥 拉取最新代码..."
          git pull origin test
          echo "🐳 重新构建并启动测试环境..."
          docker-compose -f docker-compose.backend-test.yml down
          docker-compose -f docker-compose.backend-test.yml build
          docker-compose -f docker-compose.backend-test.yml up -d
          echo "⏳ 等待服务启动..."
          sleep 30
          echo "🔍 检查服务状态..."
          docker-compose -f docker-compose.backend-test.yml ps
        EOF
      else
        echo "⚠️ SSH配置未完成，跳过自动部署"
        echo "💡 请手动在服务器上执行："
        echo "   cd /opt/9-wings-erp"
        echo "   git pull origin test"
        echo "   docker-compose -f docker-compose.backend-test.yml up -d --build"
      fi
  environment:
    name: test
    url: http://$TEST_SERVER_HOST:8080
  only:
    - test

# 部署到生产环境（简化版）
deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client curl git
  script:
    - echo "🚀 部署到生产环境..."
    - echo "💡 使用本地Docker Compose构建和部署"
    - |
      if [ -n "$SSH_PRIVATE_KEY" ] && [ -n "$PROD_SERVER_HOST" ]; then
        eval $(ssh-agent -s)
        echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        ssh-keyscan -H $PROD_SERVER_HOST >> ~/.ssh/known_hosts
        
        ssh $SSH_USER@$PROD_SERVER_HOST << 'EOF'
          cd /opt/9-wings-erp
          echo "📥 拉取最新代码..."
          git pull origin main
          echo "🐳 重新构建并启动生产环境..."
          docker-compose -f docker-compose.backend-prod.yml down
          docker-compose -f docker-compose.backend-prod.yml build
          docker-compose -f docker-compose.backend-prod.yml up -d
          echo "⏳ 等待服务启动..."
          sleep 60
          echo "🔍 检查服务状态..."
          docker-compose -f docker-compose.backend-prod.yml ps
        EOF
      else
        echo "⚠️ SSH配置未完成，跳过自动部署"
        echo "💡 请手动在服务器上执行："
        echo "   cd /opt/9-wings-erp"
        echo "   git pull origin main"
        echo "   docker-compose -f docker-compose.backend-prod.yml up -d --build"
      fi
  environment:
    name: production
    url: http://$PROD_SERVER_HOST
  when: manual
  only:
    - main
